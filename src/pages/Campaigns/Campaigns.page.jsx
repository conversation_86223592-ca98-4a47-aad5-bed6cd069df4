import React, { useEffect, useState, useMemo, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { Card, Form, Row, Col, Button } from "react-bootstrap";
import { FaMagnifyingGlass } from "react-icons/fa6";
import { TiArrowForward } from "react-icons/ti";
import DataTableComponent from "../../components/CustomDataTable/DataTable.component";
import FetchingDataLoading from "../../components/LoadingAnimation/FetchingDataLoading";
import PaginationRecordsForReports from "../../components/Reports/PaginationRecordsForReports";
import { useTranslatedColumns } from "../../components/Reports/ColumnsForTables.module";
import campaignsService from "../../services/campaigns";
import useDebounce from "../../utils/use-debounce";
import {
  format,
  startOfDay,
  endOfDay,
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
  startOfYear,
  endOfYear,
  subDays,
  subWeeks,
  subMonths,
} from "date-fns";
import "./Campaigns.page.css";
import { FaCalendarAlt } from "react-icons/fa";

export default function CampaignsPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { campaignsColumns } = useTranslatedColumns();
  const [loading, setLoading] = useState(false);
  const [campaigns, setCampaigns] = useState([]);
  const [campaignsApiResponse, setCampaignsApiResponse] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");

  // Date filtering state
  const [dateRange, setDateRange] = useState("custom");
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);

  // Debounced search term
  const debouncedSearchTerm = useDebounce(searchTerm, 1000);

  // Date range options
  const dateRangeOptions = useMemo(
    () => [
      { value: "custom", label: t("filters.custom", "Custom") },
      { value: "today", label: t("filters.today", "Today") },
      { value: "yesterday", label: t("filters.yesterday", "Yesterday") },
      { value: "thisWeek", label: t("filters.thisWeek", "This Week") },
      { value: "lastWeek", label: t("filters.lastWeek", "Last Week") },
      { value: "thisMonth", label: t("filters.thisMonth", "This Month") },
      { value: "lastMonth", label: t("filters.lastMonth", "Last Month") },
      { value: "thisYear", label: t("filters.thisYear", "This Year") },
      { value: "lastYear", label: t("filters.lastYear", "Last Year") },
    ],
    [t]
  );

  // Handle date range change
  const handleDateRangeChange = useCallback((value) => {
    setDateRange(value);
    const now = new Date();

    switch (value) {
      case "today":
        setStartDate(startOfDay(now));
        setEndDate(endOfDay(now));
        break;
      case "yesterday":
        const yesterday = subDays(now, 1);
        setStartDate(startOfDay(yesterday));
        setEndDate(endOfDay(yesterday));
        break;
      case "thisWeek":
        setStartDate(startOfWeek(now));
        setEndDate(endOfWeek(now));
        break;
      case "lastWeek":
        const lastWeekStart = startOfWeek(subWeeks(now, 1));
        const lastWeekEnd = endOfWeek(subWeeks(now, 1));
        setStartDate(lastWeekStart);
        setEndDate(lastWeekEnd);
        break;
      case "thisMonth":
        setStartDate(startOfMonth(now));
        setEndDate(endOfMonth(now));
        break;
      case "lastMonth":
        const lastMonthStart = startOfMonth(subMonths(now, 1));
        const lastMonthEnd = endOfMonth(subMonths(now, 1));
        setStartDate(lastMonthStart);
        setEndDate(lastMonthEnd);
        break;
      case "thisYear":
        setStartDate(startOfYear(now));
        setEndDate(endOfYear(now));
        break;
      case "lastYear":
        const lastYear = subMonths(now, 12);
        setStartDate(startOfYear(lastYear));
        setEndDate(endOfYear(lastYear));
        break;
      case "custom":
      default:
        // Don't change dates for custom range
        break;
    }
  }, []);

  const fetchCampaigns = useCallback(async () => {
    setLoading(true);
    try {
      // Prepare filters
      const filters = {};

      if (startDate) {
        filters.startDate = format(startDate, "yyyy-MM-dd");
      }
      if (endDate) {
        filters.endDate = format(endDate, "yyyy-MM-dd");
      }
      if (debouncedSearchTerm.trim()) {
        filters.search = debouncedSearchTerm.trim();
      }

      const response = await campaignsService.getAllCampaignsApi(
        recordsPerPage,
        currentPage,
        filters
      );

      if (
        (response?.success || response?.message === "Success") &&
        response?.data
      ) {
        // Handle nested data structure - campaigns are in response.data.data
        setCampaigns(response.data.data || []);
        setCampaignsApiResponse(response.data); // Store full API response for pagination
      } else {
        setCampaigns([]);
        setCampaignsApiResponse(null);
      }
    } catch (error) {
      console.error("Error fetching campaigns:", error);
      setCampaigns([]);
      setCampaignsApiResponse(null);
    } finally {
      setLoading(false);
    }
  }, [recordsPerPage, currentPage, startDate, endDate, debouncedSearchTerm]);

  // Effect to fetch campaigns when pagination, search, or dates change
  useEffect(() => {
    fetchCampaigns();
  }, [fetchCampaigns]);

  const handleSearchChange = (e) => {
    const newSearchTerm = e.target.value;
    setSearchTerm(newSearchTerm);
    // Reset to first page when searching (frontend filtering)
    setCurrentPage(1);
  };

  const handleViewCampaignAds = (campaign) => {
    navigate(`/campaigns/${campaign.campaign_id}/ads`, {
      state: {
        campaignInfo: {
          campaign_id: campaign.campaign_id,
          campaign_name: campaign.campaign_name,
          start_date: campaign.start_date,
          status: campaign.status,
        },
      },
    });
  };

  // Pagination handlers for campaigns
  const handleCampaignsPageChange = (url) => {
    if (!url) return;

    // Extract page and per_page from URL
    const urlParams = new URLSearchParams(url.split("?")[1]);
    const page = parseInt(
      urlParams.get("current_page") || urlParams.get("page") || "1"
    );
    const perPage = parseInt(urlParams.get("per_page") || recordsPerPage);

    setCurrentPage(page);
    setRecordsPerPage(perPage);
  };

  const handleCampaignsPageSizeChange = (size) => {
    setRecordsPerPage(size);
    setCurrentPage(1); // Reset to first page when changing page size
  };

  // Handle filter submission
  const handleFilter = () => {
    setCurrentPage(1); // Reset to first page when filtering
    fetchCampaigns();
  };

  // Handle filter reset
  const handleReset = () => {
    setDateRange("custom");
    setStartDate(null);
    setEndDate(null);
    setSearchTerm("");
    setCurrentPage(1);
    // fetchCampaigns will be called automatically due to useEffect dependencies
  };

  // Get campaigns columns from the module
  const campaignsColumnsConfig = useMemo(
    () => campaignsColumns(handleViewCampaignAds),
    [campaignsColumns, handleViewCampaignAds]
  );

  // Since we're doing server-side filtering, just use campaigns directly
  const data = useMemo(() => {
    return Array.isArray(campaigns) ? campaigns : [];
  }, [campaigns]);

  return (
    <>
      <h3 className="my-4">{t("campaignsTable.title")}</h3>
      {loading ? (
        <FetchingDataLoading />
      ) : (
        <>
          <div
            className="mb-3 content-container"
            style={{
              opacity: loading ? 0.5 : 1,
            }}
          >
            {/* Filter Section */}
            <div className="filters-card p-3 mb-3 bg-light border rounded">
              <h5 className="mb-3">{t("filters.title", "Filters")}</h5>
              <Row className="g-3">
                <Col lg={2} md={6} sm={12}>
                  <Form.Group>
                    <Form.Label>
                      {t("filters.dateRange", "Date Range")}
                    </Form.Label>
                    <Form.Select
                      value={dateRange}
                      onChange={(e) => handleDateRangeChange(e.target.value)}
                    >
                      {dateRangeOptions.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>

                <Col lg={2} md={6} sm={12}>
                  <Form.Group>
                    <Form.Label>
                      {t("filters.startDate", "Start Date")}
                    </Form.Label>
                    <div className="input-group">
                      <span className="input-group-text">
                        <FaCalendarAlt />
                      </span>
                      <Form.Control
                        type="date"
                        value={startDate ? format(startDate, "yyyy-MM-dd") : ""}
                        onChange={(e) =>
                          setStartDate(
                            e.target.value ? new Date(e.target.value) : null
                          )
                        }
                      />
                    </div>
                  </Form.Group>
                </Col>

                <Col lg={2} md={6} sm={12}>
                  <Form.Group>
                    <Form.Label>{t("filters.endDate", "End Date")}</Form.Label>
                    <div className="input-group">
                      <span className="input-group-text">
                        <FaCalendarAlt />
                      </span>
                      <Form.Control
                        type="date"
                        value={endDate ? format(endDate, "yyyy-MM-dd") : ""}
                        onChange={(e) =>
                          setEndDate(
                            e.target.value ? new Date(e.target.value) : null
                          )
                        }
                      />
                    </div>
                  </Form.Group>
                </Col>

                <Col lg={3} md={6} sm={12}>
                  <Form.Group>
                    <Form.Label>{t("filters.search", "Search")}</Form.Label>
                    <div className="input-group">
                      <Form.Control
                        type="text"
                        placeholder={t(
                          "filters.searchPlaceholder",
                          "Search campaigns..."
                        )}
                        value={searchTerm}
                        onChange={handleSearchChange}
                      />
                      <span className="input-group-text">
                        <FaMagnifyingGlass />
                      </span>
                    </div>
                  </Form.Group>
                </Col>

                <Col lg={3} md={6} sm={12} className="d-flex align-items-end">
                  <div className="d-flex gap-2">
                    <Button
                      variant="primary"
                      onClick={handleFilter}
                      className="d-flex align-items-center gap-2 submit-btn fs-6"
                    >
                      <TiArrowForward />
                      {t("buttons.filter", "Filter")}
                    </Button>
                    <Button
                      variant="outline-danger"
                      onClick={handleReset}
                      className="fs-6 rounded-3"
                    >
                      {t("buttons.reset", "Reset")}
                    </Button>
                  </div>
                </Col>
              </Row>
            </div>

            <DataTableComponent
              columns={campaignsColumnsConfig}
              data={data || []}
              loading={loading}
              initialSortBy={[]}
              hiddenColumns={[]}
              noDataFound={
                t("campaignsTable.noCampaignsFound") || "No Campaigns Found"
              }
            />

            <PaginationRecordsForReports
              onPageChange={handleCampaignsPageChange}
              links={campaignsApiResponse?.links || []}
              handlePageSizeChange={handleCampaignsPageSizeChange}
              per_page={campaignsApiResponse?.per_page || recordsPerPage}
              to={campaignsApiResponse?.to || 0}
              total={campaignsApiResponse?.total || 0}
              currentPage={campaignsApiResponse?.current_page || currentPage}
            />
          </div>
        </>
      )}
    </>
  );
}
