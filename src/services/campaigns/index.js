import apiRequest from "../../utils/apiRequest";

// Get all campaigns
const getAllCampaignsApi = async (records = 10, currentPage = 1, filters = {}) => {
    const params = new URLSearchParams({
        per_page: records,
        current_page: currentPage,
    });

    // Add date filters if provided
    if (filters.startDate) {
        params.append('start_date', filters.startDate);
    }
    if (filters.endDate) {
        params.append('end_date', filters.endDate);
    }

    // Add search filter if provided
    if (filters.search) {
        params.append('search', filters.search);
    }

    // Add page_id filter if provided
    if (filters.page_id) {
        params.append('page_id', filters.page_id);
    }

    return await apiRequest(`campaigns?${params.toString()}`, "get");
};

// Get ads for a specific campaign
const getCampaignAdsApi = async (campaignId, records = 10, currentPage = 1, filters = {}) => {
    const params = new URLSearchParams({
        per_page: records,
        current_page: currentPage,
    });

    // Add date filters if provided
    if (filters.startDate) {
        params.append('start_date', filters.startDate);
    }
    if (filters.endDate) {
        params.append('end_date', filters.endDate);
    }

    // Add search filter if provided
    if (filters.search) {
        params.append('search', filters.search);
    }

    return await apiRequest(`campaigns/${campaignId}/ads?${params.toString()}`, "get");
};

// Get campaigns for a specific page (new endpoint)
const getCampaignsApi = async (pageId) => {
    return await apiRequest(`campaigns?page_id=${pageId}`, "get");
};

// Get campaigns pagination (new endpoint)
const getCampaignsPaginationApi = async (paginationUrl) => {
    // Extract the path and query parameters from the full URL
    const url = new URL(paginationUrl);
    const pathAndQuery = url.pathname + url.search;

    return await apiRequest(`campaigns-pagination${pathAndQuery}`, "get");
};

// Get ads for a specific campaign (new endpoint)
const getAdsApi = async (campaignId) => {
    return await apiRequest(`ads?campaign_id=${campaignId}`, "get");
};

// Get ads pagination (new endpoint)
const getAdsPaginationApi = async (paginationUrl) => {
    // Extract the path and query parameters from the full URL
    const url = new URL(paginationUrl);
    const pathAndQuery = url.pathname + url.search;

    return await apiRequest(`ads-pagination${pathAndQuery}`, "get");
};

export default {
    getAllCampaignsApi,
    getCampaignAdsApi,
    getCampaignsApi,
    getCampaignsPaginationApi,
    getAdsApi,
    getAdsPaginationApi,
};
