import { useEffect, useState, useMemo, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import { Card, Form, Row, Col, Button, Table } from "react-bootstrap";
import { FaMagnifyingGlass } from "react-icons/fa6";
import { TiArrowForward } from "react-icons/ti";
import { useTable } from "react-table";
import FetchingDataLoading from "../../components/LoadingAnimation/FetchingDataLoading";
import PaginationRecordsForReports from "../../components/Reports/PaginationRecordsForReports";
import { useTranslatedColumns } from "../../components/Reports/ColumnsForTables.module";
import campaignsService from "../../services/campaigns";
import NavigateBackComponent from "../AdminDashboard/NavigateBack.component";
import useDebounce from "../../utils/use-debounce";
import {
  format,
  startOfDay,
  endOfDay,
  startOfWeek,
  endOfWeek,
  startOfMonth,
  endOfMonth,
  startOfYear,
  endOfYear,
  subDays,
  subWeeks,
  subMonths,
} from "date-fns";
import "./Campaigns.page.css";
import { FaCalendarAlt } from "react-icons/fa";

// Custom Campaign Ads Table Component with column spans
const CampaignAdsTable = ({ data, columns, noDataMessage }) => {
  const { t } = useTranslation();

  const tableInstance = useTable({
    columns,
    data,
  });

  const { getTableProps, getTableBodyProps, headerGroups, rows, prepareRow } =
    tableInstance;

  // Helper function to render header groups with proper column spans
  const renderHeaderGroups = () => {
    return headerGroups.map((headerGroup, index) => (
      <tr {...headerGroup.getHeaderGroupProps()} key={index}>
        {headerGroup.headers.map((column) => {
          // Check if this is a parent column with sub-columns
          const hasSubColumns = column.columns && column.columns.length > 0;
          const colSpan = hasSubColumns ? column.columns.length : 1;

          return (
            <th
              {...column.getHeaderProps()}
              key={column.id}
              className="text-center align-middle"
              colSpan={colSpan}
              style={{
                backgroundColor: hasSubColumns ? "#f8f9fa" : "#e9ecef",
                fontWeight: "bold",
                border: "1px solid #dee2e6",
                padding: "12px 8px",
              }}
            >
              {column.render("Header")}
            </th>
          );
        })}
      </tr>
    ));
  };

  if (!data || data.length === 0) {
    return (
      <div className="text-center py-5">
        <p className="text-muted">{noDataMessage}</p>
      </div>
    );
  }

  return (
    <div className="table-responsive">
      <Table {...getTableProps()} striped bordered hover className="mb-0">
        <thead>{renderHeaderGroups()}</thead>
        <tbody {...getTableBodyProps()}>
          {rows.map((row) => {
            prepareRow(row);
            return (
              <tr {...row.getRowProps()} key={row.id}>
                {row.cells.map((cell) => (
                  <td
                    {...cell.getCellProps()}
                    key={cell.column.id}
                    className="text-center align-middle"
                    style={{
                      border: "1px solid #dee2e6",
                      padding: "8px",
                    }}
                  >
                    {cell.render("Cell")}
                  </td>
                ))}
              </tr>
            );
          })}
        </tbody>
      </Table>
    </div>
  );
};

export default function CampaignAdsPage() {
  const { t } = useTranslation();
  const { campaignId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { adsColumns } = useTranslatedColumns();
  const [loading, setLoading] = useState(false);
  const [ads, setAds] = useState([]);
  const [adsApiResponse, setAdsApiResponse] = useState(null);
  const [adsSearchTerm, setAdsSearchTerm] = useState("");
  const [selectedCampaign, setSelectedCampaign] = useState(null);

  // Date filtering state
  const [dateRange, setDateRange] = useState("custom");
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);

  // Debounced search term
  const debouncedSearchTerm = useDebounce(adsSearchTerm, 1000);

  // Date range options
  const dateRangeOptions = useMemo(
    () => [
      { value: "custom", label: t("filters.custom", "Custom") },
      { value: "today", label: t("filters.today", "Today") },
      { value: "yesterday", label: t("filters.yesterday", "Yesterday") },
      { value: "thisWeek", label: t("filters.thisWeek", "This Week") },
      { value: "lastWeek", label: t("filters.lastWeek", "Last Week") },
      { value: "thisMonth", label: t("filters.thisMonth", "This Month") },
      { value: "lastMonth", label: t("filters.lastMonth", "Last Month") },
      { value: "thisYear", label: t("filters.thisYear", "This Year") },
      { value: "lastYear", label: t("filters.lastYear", "Last Year") },
    ],
    [t]
  );

  // Handle date range change
  const handleDateRangeChange = useCallback((value) => {
    setDateRange(value);
    const now = new Date();

    switch (value) {
      case "today":
        setStartDate(startOfDay(now));
        setEndDate(endOfDay(now));
        break;
      case "yesterday":
        const yesterday = subDays(now, 1);
        setStartDate(startOfDay(yesterday));
        setEndDate(endOfDay(yesterday));
        break;
      case "thisWeek":
        setStartDate(startOfWeek(now));
        setEndDate(endOfWeek(now));
        break;
      case "lastWeek":
        const lastWeekStart = startOfWeek(subWeeks(now, 1));
        const lastWeekEnd = endOfWeek(subWeeks(now, 1));
        setStartDate(lastWeekStart);
        setEndDate(lastWeekEnd);
        break;
      case "thisMonth":
        setStartDate(startOfMonth(now));
        setEndDate(endOfMonth(now));
        break;
      case "lastMonth":
        const lastMonthStart = startOfMonth(subMonths(now, 1));
        const lastMonthEnd = endOfMonth(subMonths(now, 1));
        setStartDate(lastMonthStart);
        setEndDate(lastMonthEnd);
        break;
      case "thisYear":
        setStartDate(startOfYear(now));
        setEndDate(endOfYear(now));
        break;
      case "lastYear":
        const lastYear = subMonths(now, 12);
        setStartDate(startOfYear(lastYear));
        setEndDate(endOfYear(lastYear));
        break;
      case "custom":
      default:
        // Don't change dates for custom range
        break;
    }
  }, []);

  useEffect(() => {
    if (campaignId) {
      // Check if campaign info was passed via navigation state
      const campaignInfo = location.state?.campaignInfo;
      if (campaignInfo) {
        setSelectedCampaign(campaignInfo);
      } else {
        // Fallback: set loading state and try to get info from API response
        setSelectedCampaign({
          campaign_id: campaignId,
          campaign_name: `Loading...`,
        });
      }

      fetchAdsForCampaign(campaignId, 1, 10);
    }
  }, [campaignId, location.state]);

  const fetchAdsForCampaign = useCallback(
    async (campaignId) => {
      setLoading(true);
      try {
        const response = await campaignsService.getAdsApi(campaignId);

        if (
          (response?.status === 200 ||
            response?.success ||
            response?.message === "Success") &&
          response?.data
        ) {
          // Handle nested data structure - ads are in response.data.data
          setAds(response.data.data || []);
          setAdsApiResponse(response.data); // Store full API response for pagination

          // Only try to extract campaign info from response if we don't already have it from navigation state
          if (!location.state?.campaignInfo) {
            if (response.data.campaign_name) {
              // If the API response includes campaign information
              setSelectedCampaign({
                campaign_id: campaignId,
                campaign_name: response.data.campaign_name,
              });
            } else if (response.data.data && response.data.data.length > 0) {
              // If ads exist, we can try to get campaign info from the first ad
              const firstAd = response.data.data[0];
              if (firstAd.campaign_name) {
                setSelectedCampaign({
                  campaign_id: campaignId,
                  campaign_name: firstAd.campaign_name,
                });
              } else {
                // Fallback to campaign ID
                setSelectedCampaign({
                  campaign_id: campaignId,
                  campaign_name: `Campaign ${campaignId}`,
                });
              }
            } else {
              // No ads, fallback to campaign ID
              setSelectedCampaign({
                campaign_id: campaignId,
                campaign_name: `Campaign ${campaignId}`,
              });
            }
          }
        } else {
          setAds([]);
          setAdsApiResponse(null);
        }
      } catch (error) {
        console.error("Error fetching ads for campaign:", error);
        setAds([]);
        setAdsApiResponse(null);
      } finally {
        setLoading(false);
      }
    },
    [location.state?.campaignInfo]
  );

  const fetchAdsPagination = useCallback(async (paginationUrl) => {
    if (!paginationUrl) return;

    setLoading(true);
    try {
      const response = await campaignsService.getAdsPaginationApi(
        paginationUrl
      );

      if (
        (response?.status === 200 ||
          response?.success ||
          response?.message === "Success") &&
        response?.data
      ) {
        // Handle nested data structure - ads are in response.data.data
        setAds(response.data.data || []);
        setAdsApiResponse(response.data); // Store full API response for pagination
      } else {
        setAds([]);
        setAdsApiResponse(null);
      }
    } catch (error) {
      console.error("Error fetching ads pagination:", error);
      setAds([]);
      setAdsApiResponse(null);
    } finally {
      setLoading(false);
    }
  }, []);

  const handleAdsSearchChange = (e) => {
    const newSearchTerm = e.target.value;
    setAdsSearchTerm(newSearchTerm);
  };

  const handleBackToCampaigns = () => {
    navigate("/campaigns");
  };

  const handleViewLeads = (ad) => {
    navigate(`/ads/${ad.ad_id}/leads`, {
      state: {
        adInfo: {
          ad_id: ad.ad_id,
          ad_name: ad.ad_name,
          campaign_name: ad.campaign_name,
          page_name: ad.page_name,
        },
      },
    });
  };

  // Frontend filtering logic
  const filteredAds = useMemo(() => {
    if (!ads || ads.length === 0) return [];

    return ads.filter((ad) => {
      // Search filter
      if (debouncedSearchTerm.trim()) {
        const searchTerm = debouncedSearchTerm.toLowerCase();
        const adName = (ad.ad_name || "").toLowerCase();
        if (!adName.includes(searchTerm)) {
          return false;
        }
      }

      // Date filters
      if (startDate || endDate) {
        const adStartDate = ad.start_date ? new Date(ad.start_date) : null;

        if (startDate && adStartDate) {
          if (adStartDate < startOfDay(startDate)) {
            return false;
          }
        }

        if (endDate && adStartDate) {
          if (adStartDate > endOfDay(endDate)) {
            return false;
          }
        }
      }

      return true;
    });
  }, [ads, debouncedSearchTerm, startDate, endDate]);

  // Pagination handlers for ads
  const handleAdsPageChange = (url) => {
    if (!url) return;
    fetchAdsPagination(url);
  };

  const handleAdsPageSizeChange = (size) => {
    if (!campaignId) return;
    fetchAdsForCampaign(campaignId); // Refetch from main endpoint for page size changes
  };

  // Handle filter submission
  const handleFilter = () => {
    if (!campaignId) return;
    fetchAdsForCampaign(campaignId, 1, 10);
  };

  // Handle filter reset
  const handleReset = () => {
    setDateRange("custom");
    setStartDate(null);
    setEndDate(null);
    setAdsSearchTerm("");
    if (campaignId) {
      fetchAdsForCampaign(campaignId, 1, 10);
    }
  };

  // Get ads columns from the module
  const adsColumnsConfig = useMemo(
    () => adsColumns(handleViewLeads),
    [adsColumns, handleViewLeads]
  );

  // Use filtered ads for frontend filtering
  const data = useMemo(() => {
    return Array.isArray(filteredAds) ? filteredAds : [];
  }, [filteredAds]);

  return (
    <>
      <NavigateBackComponent
        title={`${t("campaignsTable.adsForCampaign")} ${
          selectedCampaign?.campaign_name || ""
        }`}
        onBack={handleBackToCampaigns}
      />

      {loading ? (
        <FetchingDataLoading />
      ) : (
        <>
          <div
            className="mb-3 content-container"
            style={{
              opacity: loading ? 0.5 : 1,
            }}
          >
            {/* Filter Section */}
            <div className="filters-card p-3 mb-3 bg-light border rounded">
              <h5 className="mb-3">{t("filters.title", "Filters")}</h5>
              <Row className="g-3">
                <Col lg={3} md={6} sm={12}>
                  <Form.Group>
                    <Form.Label>
                      {t("filters.dateRange", "Date Range")}
                    </Form.Label>
                    <Form.Select
                      value={dateRange}
                      onChange={(e) => handleDateRangeChange(e.target.value)}
                    >
                      {dateRangeOptions.map((option) => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>

                <Col lg={3} md={6} sm={12}>
                  <Form.Group>
                    <Form.Label>
                      {t("filters.startDate", "Start Date")}
                    </Form.Label>
                    <div className="input-group">
                      <span className="input-group-text">
                        <FaCalendarAlt />
                      </span>
                      <Form.Control
                        type="date"
                        value={startDate ? format(startDate, "yyyy-MM-dd") : ""}
                        onChange={(e) =>
                          setStartDate(
                            e.target.value ? new Date(e.target.value) : null
                          )
                        }
                      />
                    </div>
                  </Form.Group>
                </Col>

                <Col lg={3} md={6} sm={12}>
                  <Form.Group>
                    <Form.Label>{t("filters.endDate", "End Date")}</Form.Label>
                    <div className="input-group">
                      <span className="input-group-text">
                        <FaCalendarAlt />
                      </span>
                      <Form.Control
                        type="date"
                        value={endDate ? format(endDate, "yyyy-MM-dd") : ""}
                        onChange={(e) =>
                          setEndDate(
                            e.target.value ? new Date(e.target.value) : null
                          )
                        }
                      />
                    </div>
                  </Form.Group>
                </Col>

                <Col lg={3} md={6} sm={12}>
                  <Form.Group>
                    <Form.Label>{t("filters.search", "Search")}</Form.Label>
                    <div className="input-group">
                      <Form.Control
                        type="text"
                        placeholder={t(
                          "filters.searchPlaceholder",
                          "Search ads..."
                        )}
                        value={adsSearchTerm}
                        onChange={handleAdsSearchChange}
                      />
                      <span className="input-group-text">
                        <FaMagnifyingGlass />
                      </span>
                    </div>
                  </Form.Group>
                </Col>
              </Row>

              <Row className="mt-3">
                <Col className="d-flex gap-2">
                  <Button
                    variant="primary"
                    onClick={handleFilter}
                    className="d-flex align-items-center gap-2"
                  >
                    <TiArrowForward />
                    {t("buttons.filter", "Filter")}
                  </Button>
                  <Button variant="outline-secondary" onClick={handleReset}>
                    {t("buttons.reset", "Reset")}
                  </Button>
                </Col>
              </Row>
            </div>

            <CampaignAdsTable
              data={data || []}
              columns={adsColumnsConfig}
              noDataMessage={t("adsTable.noAdsFound") || "No Ads Found"}
            />

            <PaginationRecordsForReports
              onPageChange={handleAdsPageChange}
              links={adsApiResponse?.links || []}
              handlePageSizeChange={handleAdsPageSizeChange}
              per_page={adsApiResponse?.per_page || 10}
              to={adsApiResponse?.to || 0}
              total={adsApiResponse?.total || 0}
              currentPage={adsApiResponse?.current_page || 1}
            />
          </div>
        </>
      )}
    </>
  );
}
